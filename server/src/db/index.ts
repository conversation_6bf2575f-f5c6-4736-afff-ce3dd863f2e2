import { Pool } from 'pg';

// Create a new Pool instance.
// Configure it using environment variables for connection details
// (PGUSER, PGHOST, PGDATABASE, PGPASSWORD, PGPORT)
// Provide sensible defaults for local development if these variables are not set.
const pool = new Pool({
    user: process.env.PGUSER || 'codespace', // Use codespace user
    host: process.env.PGHOST || '127.0.0.1', // Use localhost IP for trust auth
    database: process.env.PGDATABASE || 'booking_system_dev', // Use our created database
    password: process.env.PGPASSWORD || '', // No password for local development
    port: process.env.PGPORT ? parseInt(process.env.PGPORT, 10) : 5432, // Default port
});

/**
 * Executes a SQL query against the database.
 * @param text The SQL query string. This can include placeholders like $1, $2.
 * @param params An array of parameters to substitute into the query.
 * @returns A promise that resolves with the query result.
 */
export const query = async (text: string, params?: any[]) => {
    try {
        return await pool.query(text, params);
    } catch (error) {
        console.error('Database query error:', error);
        throw new Error('Database is not available. Please check your database connection.');
    }
};

/**
 * Tests the database connection by executing a simple query.
 * This function is called when the module is loaded to verify connectivity.
 */
async function testConnection() {
    try {
        const result = await pool.query('SELECT NOW()');
        console.log('Database connected successfully. Current time from DB:', result.rows[0].now);
    } catch (err) {
        console.error('Database connection error:', err);
        console.log('Application will continue without database functionality.');
        // Don't exit the process, allow the app to run without DB
    }
}

// Call the test connection function when the module is loaded.
testConnection();

// Optional: Export the pool itself if direct access is needed for transactions, etc.
// export { pool };

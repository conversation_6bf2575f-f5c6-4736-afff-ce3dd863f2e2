import { Pool } from 'pg';

// Create a new Pool instance.
// Configure it using environment variables for connection details
// (PGUSER, PGHOST, PGDATABASE, PGPASSWORD, PGPORT)
// Provide sensible defaults for local development if these variables are not set.
const pool = new Pool({
    user: process.env.PGUSER || 'codespace', // Use codespace user for peer auth
    host: process.env.PGHOST || '/var/run/postgresql', // Use Unix socket
    database: process.env.PGDATABASE || 'postgres', // Use default postgres database
    password: process.env.PGPASSWORD || '', // No password for local development
    port: process.env.PGPORT ? parseInt(process.env.PGPORT, 10) : 5432, // Default port
});

/**
 * Executes a SQL query against the database.
 * @param text The SQL query string. This can include placeholders like $1, $2.
 * @param params An array of parameters to substitute into the query.
 * @returns A promise that resolves with the query result.
 */
export const query = (text: string, params?: any[]) => pool.query(text, params);

/**
 * Tests the database connection by executing a simple query.
 * This function is called when the module is loaded to verify connectivity.
 */
async function testConnection() {
    try {
        const result = await pool.query('SELECT NOW()');
        console.log('Database connected successfully. Current time from DB:', result.rows[0].now);
    } catch (err) {
        console.error('Database connection error:', err);
        // Depending on the application's needs, you might want to exit the process
        // if the database connection fails at startup.
        // process.exit(1);
    }
}

// Call the test connection function when the module is loaded.
testConnection();

// Optional: Export the pool itself if direct access is needed for transactions, etc.
// export { pool };

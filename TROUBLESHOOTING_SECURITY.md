# Troubleshooting & Security Guide

## Table of Contents
1. [Common Issues and Solutions](#common-issues-and-solutions)
2. [Security Best Practices](#security-best-practices)
3. [Production Deployment Security](#production-deployment-security)
4. [Monitoring and Logging](#monitoring-and-logging)
5. [Emergency Procedures](#emergency-procedures)

## Common Issues and Solutions

### Database Connection Issues

#### Issue: Connection Refused (ECONNREFUSED)
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Diagnosis Steps:**
```bash
# 1. Check if PostgreSQL is running
sudo service postgresql status

# 2. Check if port 5432 is listening
netstat -tlnp | grep 5432
lsof -i :5432

# 3. Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-12-main.log
```

**Solutions:**
```bash
# Start PostgreSQL service
sudo service postgresql start

# Enable auto-start on boot
sudo systemctl enable postgresql

# If port conflict exists, find and kill conflicting process
sudo lsof -ti:5432 | xargs sudo kill -9
```

#### Issue: Authentication Failed
```
FATAL: Peer authentication failed for user "postgres"
FATAL: password authentication failed for user "codespace"
```

**Diagnosis:**
```bash
# Check authentication configuration
sudo cat /etc/postgresql/12/main/pg_hba.conf | grep -E "^(local|host)"
```

**Solutions:**
```bash
# For development: Enable trust authentication
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     trust/' /etc/postgresql/12/main/pg_hba.conf
sudo sed -i 's/host    all             all             127.0.0.1\/32            md5/host    all             all             127.0.0.1\/32            trust/' /etc/postgresql/12/main/pg_hba.conf

# Restart PostgreSQL
sudo service postgresql restart

# For production: Set proper password
psql -U postgres -h 127.0.0.1 -d postgres -c "ALTER USER imgdream_app PASSWORD 'secure_password';"
```

#### Issue: Role/Database Does Not Exist
```
FATAL: role "codespace" does not exist
FATAL: database "booking_system_dev" does not exist
```

**Solutions:**
```bash
# Create user
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE USER codespace WITH CREATEDB SUPERUSER;"

# Create database
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE DATABASE booking_system_dev;"

# Grant permissions
psql -U postgres -h 127.0.0.1 -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE booking_system_dev TO codespace;"
```

### Application Startup Issues

#### Issue: Port Already in Use
```
Error: listen EADDRINUSE: address already in use :::5000
Error: listen EADDRINUSE: address already in use :::8080
```

**Solutions:**
```bash
# Find processes using ports
lsof -i :5000
lsof -i :8080

# Kill specific process
kill <PID>

# Kill all node processes (use with caution)
pkill -f node

# Use different ports
PORT=5001 npm run dev:server
```

#### Issue: Module Not Found Errors
```
Error: Cannot find module 'express'
Error: Cannot find module 'imgdreamsupport-platform'
```

**Solutions:**
```bash
# Clear and reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Install specific workspace dependencies
npm run install:all

# Clear npm cache
npm cache clean --force

# Check for platform module issues
cd platform && npm run build
```

#### Issue: TypeScript Compilation Errors
```
Error: Cannot find name 'Express'
Error: Property 'query' does not exist on type 'Pool'
```

**Solutions:**
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Install missing type definitions
npm install --save-dev @types/express @types/pg @types/node

# Clear TypeScript cache
rm -rf dist/ .tsbuildinfo

# Rebuild platform types
cd platform && npm run build
```

### Frontend Issues

#### Issue: Blank Screen in Browser
**Diagnosis:**
```bash
# Check browser console for errors
# Open Developer Tools (F12) → Console

# Check if API calls are working
curl http://localhost:8080/api/blog-posts

# Check Vite proxy configuration
cat client/vite.config.ts
```

**Solutions:**
```bash
# Verify proxy configuration in vite.config.ts
# Ensure server is running on port 5000
# Check for JavaScript errors in browser console
# Clear browser cache and reload
```

#### Issue: API Calls Failing (404/500 errors)
**Diagnosis:**
```bash
# Test API endpoints directly
curl http://localhost:5000/api/blog-posts
curl http://localhost:8080/api/blog-posts

# Check server logs for errors
# Check network tab in browser developer tools
```

**Solutions:**
```bash
# Verify proxy configuration
# Check CORS settings in server
# Ensure both client and server are running
# Verify API endpoint URLs
```

### Email Service Issues

#### Issue: Email Sending Failures
```
Error: Invalid login: 535 Authentication failed
Error: Connection timeout
```

**Solutions:**
```bash
# For development: Check Ethereal account creation
# Check server logs for Ethereal credentials

# For production: Verify SMTP credentials
# Test SMTP connection manually
telnet smtp.sendgrid.net 587

# Check email service configuration
# Verify firewall settings for SMTP ports
```

## Security Best Practices

### Development Environment Security

#### Secure Configuration
```bash
# Use environment variables for all sensitive data
# Never commit .env files with real credentials
# Use different credentials for each environment
# Implement proper error handling to avoid information leakage
```

#### Database Security
```sql
-- Create limited user for application
CREATE USER imgdream_app WITH PASSWORD 'secure_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE imgdreamsupport_prod TO imgdream_app;
GRANT USAGE ON SCHEMA public TO imgdream_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO imgdream_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO imgdream_app;

-- Revoke unnecessary permissions
REVOKE CREATE ON SCHEMA public FROM imgdream_app;
REVOKE ALL ON DATABASE postgres FROM imgdream_app;
```

#### API Security
```javascript
// Implement rate limiting
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// Input validation
const { body, validationResult } = require('express-validator');
app.post('/api/bookings', [
  body('start_time').isISO8601(),
  body('end_time').isISO8601(),
  body('title').isLength({ min: 1, max: 255 }),
], (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  // Process request
});

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:8080',
  credentials: true
}));
```

### Production Security

#### Environment Variables Security
```bash
# Use strong, unique secrets
JWT_SECRET=$(openssl rand -base64 32)
SESSION_SECRET=$(openssl rand -base64 64)

# Use secure database passwords
PGPASSWORD=$(openssl rand -base64 24)

# Restrict CORS origins
CORS_ORIGIN=https://imgdreamsupport.com

# Disable debug mode
NODE_ENV=production
VITE_DEBUG_MODE=false
```

#### HTTPS Configuration
```javascript
// Force HTTPS in production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}

// Security headers
const helmet = require('helmet');
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
```

#### Database Security
```bash
# Use SSL connections
PGSSL=require
PGSSLMODE=require

# Connection string with SSL
DATABASE_URL=********************************/db?sslmode=require
```

### Authentication and Authorization

#### JWT Implementation
```javascript
const jwt = require('jsonwebtoken');

// Generate token
const generateToken = (user) => {
  return jwt.sign(
    { id: user.id, email: user.email },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// Verify token middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.sendStatus(401);
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};
```

#### Password Security
```javascript
const bcrypt = require('bcrypt');

// Hash password
const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

// Verify password
const verifyPassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};
```

## Production Deployment Security

### Infrastructure Security

#### Server Hardening
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw enable
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw deny 5432   # Block direct database access

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

#### Database Security
```bash
# Secure PostgreSQL installation
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'strong_password';"

# Configure PostgreSQL for production
# Edit /etc/postgresql/12/main/postgresql.conf
listen_addresses = 'localhost'
ssl = on
ssl_cert_file = '/path/to/server.crt'
ssl_key_file = '/path/to/server.key'

# Edit /etc/postgresql/12/main/pg_hba.conf
hostssl all all 0.0.0.0/0 scram-sha-256
```

#### Application Security
```bash
# Run application as non-root user
sudo useradd -m -s /bin/bash imgdream
sudo su - imgdream

# Use process manager
npm install -g pm2
pm2 start server/dist/index.js --name imgdream-api
pm2 startup
pm2 save
```

### Monitoring and Logging

#### Application Logging
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

// Log security events
const logSecurityEvent = (event, details) => {
  logger.warn('Security Event', {
    event,
    details,
    timestamp: new Date().toISOString(),
    ip: details.ip,
    userAgent: details.userAgent
  });
};
```

#### Database Monitoring
```sql
-- Monitor failed login attempts
SELECT * FROM pg_stat_activity WHERE state = 'active';

-- Check for suspicious queries
SELECT query, state, query_start 
FROM pg_stat_activity 
WHERE query NOT LIKE '%pg_stat_activity%';

-- Monitor database size
SELECT pg_size_pretty(pg_database_size('imgdreamsupport_prod'));
```

#### System Monitoring
```bash
# Monitor system resources
htop
iotop
netstat -tulpn

# Monitor logs
tail -f /var/log/nginx/access.log
tail -f /var/log/postgresql/postgresql-12-main.log
tail -f logs/combined.log
```

## Emergency Procedures

### Security Incident Response

#### Suspected Data Breach
1. **Immediate Actions:**
   ```bash
   # Disable affected user accounts
   # Rotate all API keys and secrets
   # Enable additional logging
   # Backup current database state
   ```

2. **Investigation:**
   ```bash
   # Check access logs
   grep "suspicious_pattern" /var/log/nginx/access.log
   
   # Check database logs
   grep "ERROR\|FATAL" /var/log/postgresql/postgresql-12-main.log
   
   # Check application logs
   grep "error\|unauthorized" logs/combined.log
   ```

3. **Recovery:**
   ```bash
   # Update all passwords and secrets
   # Apply security patches
   # Restore from clean backup if necessary
   # Implement additional security measures
   ```

#### Database Recovery
```bash
# Create emergency backup
pg_dump -U imgdream_app -h localhost imgdreamsupport_prod > emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
psql -U imgdream_app -h localhost imgdreamsupport_prod < backup_file.sql

# Check data integrity
psql -U imgdream_app -h localhost imgdreamsupport_prod -c "SELECT COUNT(*) FROM users;"
```

#### Service Recovery
```bash
# Restart services
pm2 restart all
sudo systemctl restart nginx
sudo systemctl restart postgresql

# Check service status
pm2 status
sudo systemctl status nginx
sudo systemctl status postgresql

# Monitor logs for errors
pm2 logs
tail -f /var/log/nginx/error.log
```

This comprehensive troubleshooting and security guide provides the necessary information to maintain and secure the IMG Dream Support application in both development and production environments.

# IMG Dream Support

A comprehensive web application providing services and resources for International Medical Graduates (IMGs), including booking systems, study partner matching, and educational resources.

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v16 or later)
- **npm** (v8 or later)
- **PostgreSQL** (v12 or later)

### Installation
```bash
# Clone repository
git clone https://github.com/sonafouo/imgdreamsupport.git
cd imgdreamsupport

# Install all dependencies
npm run install:all

# Setup database (see detailed instructions below)
# Start development servers
npm run dev
```

**🌐 Access the application:**
- **Frontend**: http://localhost:8080
- **Backend API**: http://localhost:5000

## 📁 Project Structure

```
imgdreamsupport/
├── client/          # React frontend (Vite + TypeScript)
├── server/          # Express backend (Node.js + TypeScript)
├── platform/        # Shared types and utilities
├── docs/           # Additional documentation
└── *.md            # Setup and configuration guides
```

## 📚 Documentation

### Essential Setup Guides
- **[Complete Setup Guide](SETUP_GUIDE.md)** - Comprehensive installation and configuration
- **[Database Configuration](DATABASE_CONFIGURATION.md)** - PostgreSQL setup and troubleshooting
- **[Environment Variables](ENVIRONMENT_VARIABLES.md)** - All configuration options
- **[Troubleshooting & Security](TROUBLESHOOTING_SECURITY.md)** - Common issues and security practices

### Quick Reference
- **Database**: PostgreSQL with booking system schema
- **Authentication**: Trust (development) / scram-sha-256 (production)
- **Email**: Ethereal (development) / SMTP (production)
- **Proxy**: Vite proxy forwards `/api` to backend

## ⚡ Development Setup

### 1. Database Setup (Critical)

**PostgreSQL must be configured correctly for the application to work.**

```bash
# Install and start PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo service postgresql start

# Configure authentication (development)
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     trust/' /etc/postgresql/12/main/pg_hba.conf
sudo sed -i 's/host    all             all             127.0.0.1\/32            md5/host    all             all             127.0.0.1\/32            trust/' /etc/postgresql/12/main/pg_hba.conf
sudo service postgresql restart

# Create user and database
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE USER codespace WITH CREATEDB SUPERUSER;"
psql -U codespace -h 127.0.0.1 -d postgres -c "CREATE DATABASE booking_system_dev;"
```

### 2. Environment Configuration

**Server environment variables** (`server/.env`):
```bash
PGUSER=codespace
PGHOST=127.0.0.1
PGDATABASE=booking_system_dev
PGPASSWORD=
PGPORT=5432
PORT=5000
NODE_ENV=development
ADMIN_EMAIL=<EMAIL>
```

**Client environment variables** (`client/.env`):
```bash
VITE_API_URL=http://localhost:5000
VITE_APP_NAME=IMG Dream Support
VITE_DEBUG_MODE=true
```

### 3. Start Development

```bash
# Build platform package first
cd platform && npm run build && cd ..

# Start both client and server
npm run dev

# Or start individually
npm run dev:client  # http://localhost:8080
npm run dev:server  # http://localhost:5000
```

## 🛠 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start both client and server concurrently |
| `npm run dev:client` | Start only the React frontend (port 8080) |
| `npm run dev:server` | Start only the Express backend (port 5000) |
| `npm run build` | Build entire project for production |
| `npm run build:client` | Build only the client |
| `npm run build:server` | Build only the server |
| `npm run start` | Start production server |
| `npm run install:all` | Install dependencies for all workspaces |

## 🏗 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Radix UI** for accessible components
- **React Query** for data fetching
- **React Router** for navigation
- **Framer Motion** for animations

### Backend
- **Node.js** with Express
- **TypeScript** for type safety
- **PostgreSQL** for database
- **Nodemailer** for email services
- **CORS** for cross-origin requests

### Database Schema
- **Users**: User management and authentication
- **Availability**: Booking availability rules
- **Bookings**: Appointment scheduling system

## 🔧 Common Issues & Solutions

### Database Connection Error
```bash
# Error: ECONNREFUSED 127.0.0.1:5432
sudo service postgresql start
```

### Authentication Failed
```bash
# Error: Peer authentication failed
# Check DATABASE_CONFIGURATION.md for detailed fix
```

### Blank Screen in Browser
```bash
# Verify proxy configuration in client/vite.config.ts
# Ensure both client and server are running
```

### Port Already in Use
```bash
# Find and kill process using port
lsof -i :5000
kill <PID>
```

**📖 For detailed troubleshooting, see [TROUBLESHOOTING_SECURITY.md](TROUBLESHOOTING_SECURITY.md)**

## 🔒 Security Features

- **Environment-based configuration**
- **Database authentication controls**
- **CORS protection**
- **Input validation**
- **Rate limiting ready**
- **Secure email handling**

## 🚀 Deployment

### Production Checklist
- [ ] Set strong database passwords
- [ ] Configure HTTPS
- [ ] Set production environment variables
- [ ] Enable rate limiting
- [ ] Configure proper CORS origins
- [ ] Set up monitoring and logging

**📖 See [SETUP_GUIDE.md](SETUP_GUIDE.md) for complete production deployment instructions**

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 📞 Support

If you encounter issues:
1. Check the [troubleshooting guide](TROUBLESHOOTING_SECURITY.md)
2. Review [database configuration](DATABASE_CONFIGURATION.md)
3. Verify [environment variables](ENVIRONMENT_VARIABLES.md)
4. Open an issue with detailed error messages

**🎯 Current Status**: ✅ Fully functional with PostgreSQL database, API proxy, and email services configured.



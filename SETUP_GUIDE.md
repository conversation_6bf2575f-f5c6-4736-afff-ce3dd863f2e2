# IMG Dream Support - Complete Setup Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [PostgreSQL Database Setup](#postgresql-database-setup)
3. [Environment Variables](#environment-variables)
4. [Development Environment Setup](#development-environment-setup)
5. [Troubleshooting](#troubleshooting)
6. [Security Considerations](#security-considerations)

## Prerequisites

Before setting up the IMG Dream Support application, ensure you have the following installed:

- **Node.js** (v16 or later)
- **npm** (v8 or later)
- **PostgreSQL** (v12 or later)
- **Git**

### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Memory**: Minimum 4GB RAM
- **Storage**: At least 2GB free space

## PostgreSQL Database Setup

### 1. Installation

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

#### macOS (using Homebrew)
```bash
brew install postgresql
brew services start postgresql
```

#### Windows
Download and install from [PostgreSQL official website](https://www.postgresql.org/download/windows/)

### 2. Service Management

#### Start PostgreSQL Service
```bash
# Ubuntu/Debian
sudo service postgresql start

# macOS
brew services start postgresql

# Check status
sudo service postgresql status
```

### 3. Authentication Configuration

The application requires specific authentication settings for development. Edit the PostgreSQL configuration:

#### Locate Configuration File
```bash
# Find pg_hba.conf location
sudo find /etc -name "pg_hba.conf" 2>/dev/null
# Common locations:
# Ubuntu: /etc/postgresql/12/main/pg_hba.conf
# macOS: /usr/local/var/postgres/pg_hba.conf
```

#### Configure Authentication (Development Only)
Edit `/etc/postgresql/12/main/pg_hba.conf`:

```bash
sudo nano /etc/postgresql/12/main/pg_hba.conf
```

**Replace these lines:**
```
local   all             all                                     peer
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
```

**With these lines (for development):**
```
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 md5
```

#### Restart PostgreSQL
```bash
sudo service postgresql restart
```

### 4. Database and User Setup

#### Create Database User
```bash
# Connect as postgres user
psql -U postgres -h 127.0.0.1 -d postgres

# In PostgreSQL shell, create user and database
CREATE USER codespace WITH CREATEDB SUPERUSER;
CREATE DATABASE booking_system_dev;
GRANT ALL PRIVILEGES ON DATABASE booking_system_dev TO codespace;
\q
```

#### Verify Connection
```bash
psql -U codespace -h 127.0.0.1 -d booking_system_dev -c "SELECT version();"
```

### 5. Database Schema Setup

The database schema will be automatically created when you first run the application. The schema includes:

- **Users table**: User management
- **Availability table**: Booking availability rules
- **Bookings table**: Appointment bookings

**Schema location**: `server/src/db/schema.sql`

#### Manual Schema Creation (if needed)
```bash
psql -U codespace -h 127.0.0.1 -d booking_system_dev -f server/src/db/schema.sql
```

## Environment Variables

### Server Environment Variables

Create a `.env` file in the `server/` directory:

```bash
# Database Configuration
PGUSER=codespace
PGHOST=127.0.0.1
PGDATABASE=booking_system_dev
PGPASSWORD=
PGPORT=5432

# Server Configuration
PORT=5000
NODE_ENV=development

# Email Configuration (Ethereal for development)
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_USER=auto-generated-by-app
EMAIL_PASS=auto-generated-by-app
EMAIL_SECURE=false

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
```

### Client Environment Variables

Create a `.env` file in the `client/` directory:

```bash
# Development API URL (handled by Vite proxy)
VITE_API_URL=http://localhost:5000

# Application Configuration
VITE_APP_NAME=IMG Dream Support
VITE_APP_VERSION=1.0.0
```

### Root Environment Variables

Create a `.env` file in the root directory:

```bash
# Development Environment
NODE_ENV=development

# Service Ports
CLIENT_PORT=8080
SERVER_PORT=5000
```

## Development Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/sonafouo/imgdreamsupport.git
cd imgdreamsupport
```

### 2. Install Dependencies
```bash
# Install all dependencies (root, client, server, platform)
npm run install:all

# Or install individually
npm install
cd client && npm install
cd ../server && npm install
cd ../platform && npm install
```

### 3. Database Setup
```bash
# Ensure PostgreSQL is running
sudo service postgresql start

# Verify database connection
psql -U codespace -h 127.0.0.1 -d booking_system_dev -c "SELECT 1;"
```

### 4. Start Development Servers
```bash
# Start both client and server concurrently
npm run dev

# Or start individually
npm run dev:client  # Starts on http://localhost:8080
npm run dev:server  # Starts on http://localhost:5000
```

### 5. Verify Setup

#### Check Services
- **Client**: http://localhost:8080
- **Server API**: http://localhost:5000/api/blog-posts
- **Database**: Connection logged in server console

#### Test API Endpoints
```bash
# Test blog posts
curl http://localhost:8080/api/blog-posts

# Test services
curl http://localhost:8080/api/services

# Test study partners
curl http://localhost:8080/api/study-partners
```

## Troubleshooting

### Common PostgreSQL Issues

#### 1. Connection Refused Error
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Solutions:**
```bash
# Check if PostgreSQL is running
sudo service postgresql status

# Start PostgreSQL
sudo service postgresql start

# Check if port 5432 is in use
lsof -i :5432
```

#### 2. Authentication Failed
```
FATAL: Peer authentication failed for user "postgres"
```

**Solutions:**
- Verify `pg_hba.conf` configuration
- Ensure trust authentication is enabled for 127.0.0.1
- Restart PostgreSQL after configuration changes

#### 3. Role Does Not Exist
```
FATAL: role "codespace" does not exist
```

**Solutions:**
```bash
# Create the user
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE USER codespace WITH CREATEDB SUPERUSER;"
```

#### 4. Database Does Not Exist
```bash
# Create the database
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE DATABASE booking_system_dev;"
```

### Common Application Issues

#### 1. Port Already in Use
```bash
# Find process using port
lsof -i :5000
lsof -i :8080

# Kill process
kill <PID>
```

#### 2. Blank Screen in Browser
- Verify Vite proxy configuration in `client/vite.config.ts`
- Check browser console for errors
- Ensure server is running and accessible

#### 3. API Calls Failing
- Verify proxy configuration
- Check CORS settings in server
- Ensure both client and server are running

### Development Server Issues

#### 1. Module Not Found Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 2. TypeScript Compilation Errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Clear TypeScript cache
rm -rf dist/
```

## Security Considerations

### Development vs Production

#### Development Configuration
- **Database**: Trust authentication for localhost
- **CORS**: Permissive settings
- **Email**: Ethereal test accounts
- **Logging**: Verbose error messages

#### Production Configuration
- **Database**: Strong authentication (md5/scram-sha-256)
- **CORS**: Restricted to specific domains
- **Email**: Real SMTP service
- **Logging**: Error logging without sensitive data
- **HTTPS**: Required for all connections
- **Environment Variables**: Stored securely (not in code)

### Sensitive Information Management

#### Never Commit to Version Control
- Database passwords
- API keys
- Email credentials
- JWT secrets
- Production environment variables

#### Use Environment Variables
```bash
# Good
DATABASE_URL=postgresql://user:pass@localhost:5432/db

# Bad (hardcoded in source)
const dbUrl = "postgresql://user:pass@localhost:5432/db"
```

#### Production Environment Variables
```bash
# Production .env example
PGUSER=prod_user
PGPASSWORD=strong_random_password
PGHOST=production-db-host.com
PGDATABASE=imgdreamsupport_prod
EMAIL_HOST=smtp.sendgrid.net
EMAIL_USER=apikey
EMAIL_PASS=your_sendgrid_api_key
JWT_SECRET=your_jwt_secret_here
```

### Database Security

#### Production Database Setup
```sql
-- Create dedicated application user
CREATE USER imgdream_app WITH PASSWORD 'strong_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE imgdreamsupport_prod TO imgdream_app;
GRANT USAGE ON SCHEMA public TO imgdream_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO imgdream_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO imgdream_app;
```

#### Connection Security
- Use SSL/TLS for database connections
- Implement connection pooling
- Set connection timeouts
- Use prepared statements to prevent SQL injection

### Application Security

#### API Security
- Implement rate limiting
- Use HTTPS in production
- Validate all input data
- Implement proper authentication/authorization
- Use CORS appropriately

#### Email Security
- Use secure SMTP connections
- Validate email addresses
- Implement email rate limiting
- Use email templates to prevent injection

---

## Quick Start Checklist

- [ ] PostgreSQL installed and running
- [ ] Database user `codespace` created
- [ ] Database `booking_system_dev` created
- [ ] Authentication configured (trust for development)
- [ ] Dependencies installed (`npm run install:all`)
- [ ] Environment variables configured
- [ ] Development servers started (`npm run dev`)
- [ ] Application accessible at http://localhost:8080
- [ ] API endpoints responding correctly

For additional help, check the [troubleshooting section](#troubleshooting) or review the application logs for specific error messages.

# Environment Variables Configuration Guide

## Overview

This document provides comprehensive information about all environment variables used in the IMG Dream Support application, including sensitive configuration details, security considerations, and setup instructions for different environments.

## Environment Variable Structure

The application uses environment variables across multiple components:
- **Root Level**: Global configuration
- **Server**: Backend API and database configuration
- **Client**: Frontend build and runtime configuration
- **Platform**: Shared configuration and types

## Server Environment Variables

### Database Configuration

#### Required Variables
```bash
# PostgreSQL Connection
PGUSER=codespace                    # Database username
PGHOST=127.0.0.1                   # Database host (localhost for development)
PGDATABASE=booking_system_dev       # Database name
PGPASSWORD=                         # Database password (empty for trust auth in dev)
PGPORT=5432                         # Database port

# Alternative: Database URL (overrides individual settings)
DATABASE_URL=postgresql://codespace@127.0.0.1:5432/booking_system_dev
```

#### Development vs Production
```bash
# Development
PGUSER=codespace
PGHOST=127.0.0.1
PGDATABASE=booking_system_dev
PGPASSWORD=
PGPORT=5432

# Production
PGUSER=imgdream_app
PGHOST=production-db-host.amazonaws.com
PGDATABASE=imgdreamsupport_prod
PGPASSWORD=secure_random_password_here
PGPORT=5432
```

### Server Configuration

```bash
# Server Settings
PORT=5000                           # Server port
NODE_ENV=development                # Environment (development/production/test)
HOST=localhost                      # Server host

# CORS Configuration
CORS_ORIGIN=http://localhost:8080   # Allowed origins for CORS
CORS_CREDENTIALS=true               # Allow credentials in CORS requests
```

### Email Configuration

#### Development (Ethereal Email)
```bash
# Ethereal automatically generates test accounts
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=                         # Auto-generated by application
EMAIL_PASS=                         # Auto-generated by application
```

#### Production (SendGrid Example)
```bash
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=SG.your_sendgrid_api_key_here
EMAIL_FROM=<EMAIL>
```

#### Production (Gmail Example)
```bash
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-specific-password
EMAIL_FROM=<EMAIL>
```

### Admin Configuration

```bash
# Admin Settings
ADMIN_EMAIL=<EMAIL>    # Admin notification email
ADMIN_NAME=IMG Dream Support Admin       # Admin display name
```

### Security Configuration

```bash
# JWT Configuration (if implementing authentication)
JWT_SECRET=your_jwt_secret_key_here      # JWT signing secret
JWT_EXPIRES_IN=24h                       # Token expiration time

# Session Configuration
SESSION_SECRET=your_session_secret_here  # Session signing secret
SESSION_MAX_AGE=86400000                 # Session max age (24 hours in ms)

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000             # Rate limit window (15 minutes)
RATE_LIMIT_MAX_REQUESTS=100             # Max requests per window
```

### API Configuration

```bash
# External API Keys (if needed)
GOOGLE_MAPS_API_KEY=your_google_maps_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

## Client Environment Variables

### Build Configuration

```bash
# Vite Configuration
VITE_API_URL=http://localhost:5000      # API base URL
VITE_APP_NAME=IMG Dream Support         # Application name
VITE_APP_VERSION=1.0.0                  # Application version
VITE_APP_DESCRIPTION=Empowering International Medical Graduates
```

### Feature Flags

```bash
# Feature toggles
VITE_ENABLE_ANALYTICS=false             # Enable analytics tracking
VITE_ENABLE_CHAT=false                  # Enable chat feature
VITE_ENABLE_PAYMENTS=false              # Enable payment processing
VITE_DEBUG_MODE=true                    # Enable debug mode
```

### External Services

```bash
# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX  # Google Analytics tracking ID
VITE_HOTJAR_ID=your_hotjar_id           # Hotjar tracking ID

# Maps and Location
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_key

# Payment Processing
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
```

## Root Level Environment Variables

```bash
# Global Configuration
NODE_ENV=development                    # Global environment setting
LOG_LEVEL=debug                         # Logging level (error/warn/info/debug)

# Service Ports
CLIENT_PORT=8080                        # Frontend development server port
SERVER_PORT=5000                        # Backend server port

# Development Tools
BROWSER=chrome                          # Default browser for development
EDITOR=code                             # Default editor
```

## Environment Files Structure

### File Locations
```
imgdreamsupport/
├── .env                               # Root environment variables
├── .env.local                         # Local overrides (gitignored)
├── .env.development                   # Development-specific variables
├── .env.production                    # Production-specific variables
├── client/
│   ├── .env                          # Client environment variables
│   ├── .env.local                    # Client local overrides
│   └── .env.production               # Client production variables
└── server/
    ├── .env                          # Server environment variables
    ├── .env.local                    # Server local overrides
    └── .env.production               # Server production variables
```

### Environment File Priority
1. `.env.local` (highest priority, gitignored)
2. `.env.development` / `.env.production`
3. `.env`
4. Default values in code

## Sample Environment Files

### server/.env (Development)
```bash
# Database Configuration
PGUSER=codespace
PGHOST=127.0.0.1
PGDATABASE=booking_system_dev
PGPASSWORD=
PGPORT=5432

# Server Configuration
PORT=5000
NODE_ENV=development
CORS_ORIGIN=http://localhost:8080

# Email Configuration (Ethereal for development)
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Security (development)
JWT_SECRET=dev_jwt_secret_change_in_production
SESSION_SECRET=dev_session_secret_change_in_production
```

### client/.env (Development)
```bash
# API Configuration
VITE_API_URL=http://localhost:5000

# Application Configuration
VITE_APP_NAME=IMG Dream Support
VITE_APP_VERSION=1.0.0
VITE_DEBUG_MODE=true

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_PAYMENTS=false
```

### server/.env.production (Production Template)
```bash
# Database Configuration
PGUSER=imgdream_app
PGHOST=your-production-db-host.com
PGDATABASE=imgdreamsupport_prod
PGPASSWORD=your_secure_database_password
PGPORT=5432

# Server Configuration
PORT=5000
NODE_ENV=production
CORS_ORIGIN=https://imgdreamsupport.com

# Email Configuration
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=apikey
EMAIL_PASS=your_sendgrid_api_key
EMAIL_FROM=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Security
JWT_SECRET=your_production_jwt_secret_here
SESSION_SECRET=your_production_session_secret_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### client/.env.production (Production Template)
```bash
# API Configuration
VITE_API_URL=https://api.imgdreamsupport.com

# Application Configuration
VITE_APP_NAME=IMG Dream Support
VITE_APP_VERSION=1.0.0
VITE_DEBUG_MODE=false

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PAYMENTS=true

# External Services
VITE_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
```

## Security Best Practices

### Sensitive Information Management

#### Never Commit These Variables
```bash
# Database credentials
PGPASSWORD=*
DATABASE_URL=*

# API keys and secrets
JWT_SECRET=*
SESSION_SECRET=*
EMAIL_PASS=*
STRIPE_SECRET_KEY=*
GOOGLE_MAPS_API_KEY=*

# Production URLs and credentials
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_*
```

#### Use Strong Secrets
```bash
# Generate strong secrets
openssl rand -base64 32  # For JWT_SECRET
openssl rand -base64 64  # For SESSION_SECRET

# Example strong secrets
JWT_SECRET=K8mF2nR7vQ9wE3rT6yU8iO1pA5sD7fG0hJ2kL4zX6cV9bN3mQ8wE1rT5yU7iO0p
SESSION_SECRET=A1sD3fG5hJ7kL9zX2cV4bN6mQ8wE0rT2yU4iO6pA8sD0fG2hJ4kL6zX8cV0bN2m
```

### Environment-Specific Security

#### Development Security
- Use test/development API keys
- Enable debug logging
- Use local/test databases
- Disable production features

#### Production Security
- Use production API keys
- Disable debug logging
- Use secure database connections
- Enable all security features
- Use HTTPS for all connections
- Implement proper error handling

### Validation and Defaults

#### Environment Variable Validation
```javascript
// server/src/config/env.js
const requiredEnvVars = [
    'PGUSER',
    'PGHOST',
    'PGDATABASE',
    'JWT_SECRET',
    'ADMIN_EMAIL'
];

requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
        throw new Error(`Required environment variable ${envVar} is not set`);
    }
});
```

#### Default Values
```javascript
// server/src/config/database.js
const dbConfig = {
    user: process.env.PGUSER || 'postgres',
    host: process.env.PGHOST || 'localhost',
    database: process.env.PGDATABASE || 'imgdreamsupport',
    password: process.env.PGPASSWORD || '',
    port: parseInt(process.env.PGPORT) || 5432,
};
```

## Deployment Considerations

### Docker Environment Variables
```dockerfile
# Dockerfile
ENV NODE_ENV=production
ENV PORT=5000

# docker-compose.yml
environment:
  - NODE_ENV=production
  - PGUSER=${PGUSER}
  - PGPASSWORD=${PGPASSWORD}
  - JWT_SECRET=${JWT_SECRET}
```

### Cloud Platform Configuration

#### Heroku
```bash
# Set environment variables
heroku config:set PGUSER=your_user
heroku config:set PGPASSWORD=your_password
heroku config:set JWT_SECRET=your_secret
```

#### AWS/Vercel/Netlify
Use the platform's environment variable configuration interface to set production values.

### CI/CD Pipeline Variables
```yaml
# GitHub Actions example
env:
  NODE_ENV: test
  PGUSER: postgres
  PGDATABASE: test_db
  JWT_SECRET: test_secret
```

This comprehensive guide covers all environment variable configurations needed for the IMG Dream Support application across different environments and deployment scenarios.

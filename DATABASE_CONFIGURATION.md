# PostgreSQL Database Configuration Guide

## Overview

This document provides detailed PostgreSQL configuration instructions specifically for the IMG Dream Support application, including the exact steps needed to resolve common authentication and connection issues.

## Database Architecture

### Schema Overview
```
booking_system_dev/
├── Users (id, name, email, password_hash, created_at, updated_at)
├── Availability (id, rule_type, day_of_week, specific_date, start_time, end_time, is_unavailable, created_at, updated_at)
└── Bookings (id, user_id, start_time, end_time, title, notes, created_at, updated_at)
```

### Relationships
- `Bookings.user_id` → `Users.id` (Foreign Key)
- Indexes on `start_time`, `end_time`, `rule_type`, `day_of_week`

## Step-by-Step Configuration

### 1. PostgreSQL Installation Verification

```bash
# Check PostgreSQL version
psql --version

# Check if service is running
sudo service postgresql status

# If not running, start it
sudo service postgresql start
```

### 2. Authentication Configuration Details

#### Understanding pg_hba.conf
The `pg_hba.conf` file controls client authentication. Each line specifies:
- **Connection type**: `local` (Unix socket) or `host` (TCP/IP)
- **Database**: Which databases the rule applies to
- **User**: Which users the rule applies to
- **Address**: Client IP addresses (for host connections)
- **Method**: Authentication method

#### Current Working Configuration
```
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 md5
```

#### Authentication Methods Explained
- **trust**: No authentication required (development only)
- **peer**: Use OS username (Unix sockets only)
- **md5**: Password authentication with MD5 hashing
- **scram-sha-256**: Modern password authentication (recommended for production)

### 3. User and Database Creation

#### Create Application User
```sql
-- Connect as superuser
psql -U postgres -h 127.0.0.1 -d postgres

-- Create user with necessary privileges
CREATE USER codespace WITH CREATEDB SUPERUSER;

-- Alternative: Create with limited privileges (production)
CREATE USER imgdream_app WITH PASSWORD 'secure_password';
GRANT CREATEDB ON DATABASE postgres TO imgdream_app;
```

#### Create Application Database
```sql
-- Create database
CREATE DATABASE booking_system_dev;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE booking_system_dev TO codespace;

-- Connect to new database
\c booking_system_dev

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO codespace;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO codespace;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO codespace;
```

### 4. Database Schema Creation

#### Automatic Schema Creation
The application automatically creates tables on first run. The schema is defined in:
```
server/src/db/schema.sql
```

#### Manual Schema Creation
```bash
# Navigate to project root
cd /path/to/imgdreamsupport

# Execute schema file
psql -U codespace -h 127.0.0.1 -d booking_system_dev -f server/src/db/schema.sql
```

#### Verify Schema Creation
```sql
-- Connect to database
psql -U codespace -h 127.0.0.1 -d booking_system_dev

-- List tables
\dt

-- Describe table structure
\d users
\d availability
\d bookings

-- Check indexes
\di
```

### 5. Connection Configuration

#### Database Connection Parameters
```javascript
// server/src/db/index.ts
const pool = new Pool({
    user: process.env.PGUSER || 'codespace',
    host: process.env.PGHOST || '127.0.0.1',
    database: process.env.PGDATABASE || 'booking_system_dev',
    password: process.env.PGPASSWORD || '',
    port: process.env.PGPORT ? parseInt(process.env.PGPORT, 10) : 5432,
});
```

#### Environment Variables
```bash
# .env file in server directory
PGUSER=codespace
PGHOST=127.0.0.1
PGDATABASE=booking_system_dev
PGPASSWORD=
PGPORT=5432
```

### 6. Connection Testing

#### Test Database Connection
```bash
# Test basic connection
psql -U codespace -h 127.0.0.1 -d booking_system_dev -c "SELECT version();"

# Test with application user
psql -U codespace -h 127.0.0.1 -d booking_system_dev -c "SELECT NOW();"

# Test table access
psql -U codespace -h 127.0.0.1 -d booking_system_dev -c "SELECT COUNT(*) FROM users;"
```

#### Test Application Connection
```bash
# Start server and check logs
cd server
npm run dev

# Should see: "Database connected successfully. Current time from DB: ..."
```

## Common Configuration Issues

### Issue 1: Connection Refused
```
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Diagnosis:**
```bash
# Check if PostgreSQL is running
sudo service postgresql status

# Check if port is open
netstat -tlnp | grep 5432
```

**Solution:**
```bash
# Start PostgreSQL
sudo service postgresql start

# Enable auto-start
sudo systemctl enable postgresql
```

### Issue 2: Authentication Failed
```
FATAL: Peer authentication failed for user "postgres"
```

**Diagnosis:**
```bash
# Check current authentication method
sudo cat /etc/postgresql/12/main/pg_hba.conf | grep -E "^(local|host)"
```

**Solution:**
```bash
# Edit pg_hba.conf
sudo nano /etc/postgresql/12/main/pg_hba.conf

# Change authentication method to trust for development
# Restart PostgreSQL
sudo service postgresql restart
```

### Issue 3: Role Does Not Exist
```
FATAL: role "codespace" does not exist
```

**Solution:**
```bash
# Create the role
psql -U postgres -h 127.0.0.1 -d postgres -c "CREATE USER codespace WITH CREATEDB SUPERUSER;"
```

### Issue 4: Database Does Not Exist
```
FATAL: database "booking_system_dev" does not exist
```

**Solution:**
```bash
# Create the database
psql -U codespace -h 127.0.0.1 -d postgres -c "CREATE DATABASE booking_system_dev;"
```

### Issue 5: Permission Denied
```
ERROR: permission denied for table users
```

**Solution:**
```sql
-- Grant table permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO codespace;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO codespace;
```

## Production Configuration

### Security Hardening

#### 1. Authentication Method
```
# Use scram-sha-256 instead of trust
host    all             all             0.0.0.0/0               scram-sha-256
```

#### 2. User Privileges
```sql
-- Create limited user for application
CREATE USER imgdream_app WITH PASSWORD 'strong_random_password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE imgdreamsupport_prod TO imgdream_app;
GRANT USAGE ON SCHEMA public TO imgdream_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO imgdream_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO imgdream_app;
```

#### 3. Connection Security
```
# postgresql.conf settings
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
ssl_ca_file = 'ca.crt'
```

#### 4. Network Security
```
# Restrict connections to specific IPs
host    imgdreamsupport_prod    imgdream_app    10.0.0.0/8         scram-sha-256
host    imgdreamsupport_prod    imgdream_app    ***********/16     scram-sha-256
```

### Backup and Maintenance

#### Database Backup
```bash
# Create backup
pg_dump -U imgdream_app -h localhost -d imgdreamsupport_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
psql -U imgdream_app -h localhost -d imgdreamsupport_prod < backup_file.sql
```

#### Regular Maintenance
```sql
-- Analyze tables for query optimization
ANALYZE;

-- Vacuum to reclaim space
VACUUM;

-- Reindex for performance
REINDEX DATABASE imgdreamsupport_prod;
```

## Monitoring and Logging

### Enable Query Logging
```
# postgresql.conf
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000  # Log queries taking > 1 second
```

### Monitor Connections
```sql
-- Check active connections
SELECT * FROM pg_stat_activity WHERE state = 'active';

-- Check database size
SELECT pg_size_pretty(pg_database_size('booking_system_dev'));

-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## Performance Optimization

### Index Optimization
```sql
-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats WHERE schemaname = 'public';

-- Create additional indexes if needed
CREATE INDEX idx_bookings_user_start_time ON bookings(user_id, start_time);
CREATE INDEX idx_availability_date_time ON availability(specific_date, start_time, end_time);
```

### Connection Pooling
```javascript
// Optimize connection pool settings
const pool = new Pool({
    user: process.env.PGUSER,
    host: process.env.PGHOST,
    database: process.env.PGDATABASE,
    password: process.env.PGPASSWORD,
    port: process.env.PGPORT,
    max: 20,                    // Maximum connections
    idleTimeoutMillis: 30000,   // Close idle connections after 30s
    connectionTimeoutMillis: 2000, // Return error after 2s if no connection available
});
```

This configuration guide should provide all the necessary information for setting up and maintaining the PostgreSQL database for the IMG Dream Support application.
